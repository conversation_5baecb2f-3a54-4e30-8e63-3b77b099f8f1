./packages/common-backend/src/services/loan/loanExperianReport.ts:    mm.$match.closeDate = { $eq: null }
./packages/common-backend/src/services/loan/transactionsReport.ts:                    { isDeactivated: { $exists: true, $eq: null } },
./packages/common-backend/src/services/loan/transactionsReport.ts:                          isPrimaryForCredit: { $exists: true, $eq: null },
./packages/server/src/controllers/company.controller.ts:                    { isDeactivated: { $exists: true, $eq: null } },
./packages/server/src/controllers/company.controller.ts:                          isPrimaryForCredit: { $exists: true, $eq: null },
./packages/server/src/controllers/customer-suppliers-list.controller.ts:                      { $eq: ['$$company_id', null] },
./packages/server/src/controllers/customer-suppliers-list.controller.ts:                { $eq: ['$company_id', null] },
./packages/server/src/controllers/invoice.controller.ts:            $and: [{ $eq: ['$expiration_date', null] }],
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: ['$operation', null] },
./packages/server/src/controllers/invoice.controller.ts:              { $eq: [ownerId, null] },
./packages/server/src/controllers/invoice.controller.ts:                          { $eq: ['$company_id', null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: ['$operation', null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: ['$operation', null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: ['$operation', null] },
./packages/server/src/controllers/invoice.controller.ts:                  { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
./packages/server/src/controllers/invoice.controller.ts:                { $eq: ['$company_id', null] },
./packages/server/src/controllers/invoicesVendorStats.controller.ts:import {
  Company,
  CustomerAccount,
  Invoice,
  LoanApplication,
  Operation,
  User,
  UserRole,
} from '@linqpal/common-backend'
import {
  invoiceStatus,
  IPayablesVendors,
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PROCESSING_STATUSES,
} from '@linqpal/models/src/dictionaries'
import { PaginatedResult } from '@linqpal/models/src/dictionaries/global'
import { Request } from 'express'
import { PipelineStage, Types } from 'mongoose'
import { CompanyStatus, EInvoiceType } from '@linqpal/models'
import moment from 'moment'

export async function getTotalInvoicesDue(
  customerAccountId: string,
  supplierId: string,
): Promise<IPayablesVendors> {
  const cstNow = moment().tz('America/Chicago').toDate()

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          { customer_account_id: customerAccountId },
          { company_id: supplierId },
          { type: EInvoiceType.INVOICE },
          { isDeleted: { $ne: true } },
          { status: { $ne: invoiceStatus.draft } },
        ],
      },
    },
    {
      $lookup: {
        from: Operation.collection.name,
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              $expr: {
                $or: [
                  {
                    $and: [
                      {
                        $in: [
                          '$status',
                          [
                            OPERATION_STATUS.PLACED,
                            OPERATION_STATUS.PROCESSING,
                            OPERATION_STATUS.SUCCESS,
                            OPERATION_STATUS.FAIL,
                          ],
                        ],
                      },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                    ],
                  },
                ],
              },
            },
          },
          { $limit: 1 },
        ],
        as: 'operation',
      },
    },
    {
      $addFields: {
        operation: { $arrayElemAt: ['$operation', 0] },
      },
    },
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                {
                  $and: [
                    { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
                    { $eq: ['$totalPaidAmount', 0] },
                  ],
                },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                { $ifNull: ['$operation.amount', '$total_amount'] },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
      },
    },
    {
      $addFields: {
        isActiveInvoice: {
          $and: [
            {
              $not: {
                $in: [
                  '$status',
                  [invoiceStatus.dismissed, invoiceStatus.cancelled],
                ],
              },
            },
            {
              $or: [
                { $eq: ['$operation', null] },
                { $eq: ['$operation.status', null] },
                {
                  $and: [
                    { $ne: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                    { $gt: ['$totalRemainingAmount', 0] },
                  ],
                },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                    { $lte: [cstNow, '$expiration_date'] },
                  ],
                },
                { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
              ],
            },
            { $ne: ['$company', null] },
          ],
        },
      },
    },
    {
      $group: {
        _id: null,
        totalDueInvoices: {
          $sum: { $cond: [{ $eq: ['$isActiveInvoice', true] }, 1, 0] },
        },
        totalDueSum: {
          $sum: {
            $cond: [
              { $eq: ['$isActiveInvoice', true] },
              { $round: ['$totalRemainingAmount', 2] },
              0,
            ],
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        totalDueInvoices: 1,
        totalDueSum: 1,
      },
    },
  ]

  const [invoiceAggregation] = await Invoice.aggregate(pipeline)

  return invoiceAggregation
}

export async function getInvoicesVendorStats(
  req: Request,
): Promise<PaginatedResult<IPayablesVendors>> {
  const cstNow = moment().tz('America/Chicago').toDate()
  const builderId = req.company!._id.toString()
  const supplierId = req.query?.supplierId?.toString()
  const supplierIsInvited = req.query?.supplierIsInvited

  const userRoles = await UserRole.aggregate([
    { $match: { company_id: builderId } },
    {
      $lookup: {
        from: User.collection.name,
        as: 'user',
        localField: 'sub',
        foreignField: 'sub',
      },
    },
    { $unwind: '$user' },
  ])
  const matchConditions = userRoles.map(
    (userRole) => userRole.user.email || userRole.user.login,
  )

  const customerAccounts = await CustomerAccount.aggregate([
    {
      $match: {
        $or: [
          { email: { $in: matchConditions } },
          { phone: { $in: matchConditions } },
        ],
      },
    },
  ])

  const customerAccountIds = customerAccounts.map((ca) => ca._id.toString())

  const pipeline: PipelineStage[] = [
    {
      $match: {
        $and: [
          {
            $or: [
              { customer_account_id: { $in: customerAccountIds } },
              { payer_id: builderId },
            ],
          },
          {
            type: EInvoiceType.INVOICE,
          },
          {
            isDeleted: {
              $ne: true,
            },
          },
          {
            $or: [
              { status: { $ne: invoiceStatus.draft } },
              {
                $and: [
                  { status: { $eq: invoiceStatus.draft } },
                  { $or: [{ company_id: null }, { company_id: '' }] },
                ],
              },
            ],
          },
        ],
      },
    },
  ]

  if (supplierId) {
    pipeline.push(
      {
        $lookup: {
          from: User.collection.name,
          let: {
            email: '$supplierInvitationDetails.email',
            company_id: '$company_id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $ne: ['$$email', ''] },
                    { $ne: ['$$email', null] },
                    { $eq: ['$email', '$$email'] },
                    {
                      $or: [
                        { $eq: ['$$company_id', null] },
                        { $eq: ['$$company_id', ''] },
                      ],
                    }, // Only perform lookup if company_id is null/empty
                  ],
                },
              },
            },
            {
              $lookup: {
                from: UserRole.collection.name,
                localField: 'sub',
                foreignField: 'sub',
                as: 'userRoleDetails',
              },
            },
            {
              $unwind: {
                path: '$userRoleDetails',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: Company.collection.name,
                let: {
                  company_id: {
                    $convert: {
                      input: '$userRoleDetails.company_id',
                      to: 'objectId',
                      onError: null,
                      onNull: null,
                    },
                  },
                },
                pipeline: [
                  { $match: { $expr: { $eq: ['$_id', '$$company_id'] } } },
                  { $match: { status: CompanyStatus.Approved } },
                ],
                as: 'companyDetails',
              },
            },
            {
              $project: {
                company_id: {
                  $cond: {
                    if: { $gt: [{ $size: '$companyDetails' }, 0] },
                    then: '$userRoleDetails.company_id',
                    else: null,
                  },
                },
              },
            },
          ],
          as: 'userDetails',
        },
      },
      { $unwind: { path: '$userDetails', preserveNullAndEmptyArrays: true } },
      {
        $addFields: {
          supplier_id: {
            $cond: {
              if: {
                $or: [
                  { $eq: ['$company_id', null] },
                  { $eq: ['$company_id', ''] },
                ],
              }, // If company_id is null/empty
              then: {
                $cond: {
                  if: {
                    $or: [
                      { $eq: ['$supplierInvitationDetails.email', null] },
                      { $eq: ['$supplierInvitationDetails.email', ''] },
                      { $eq: [{ $ifNull: ['$userDetails', null] }, null] },
                      {
                        $eq: [
                          { $ifNull: ['$userDetails.company_id', null] },
                          null,
                        ],
                      },
                      {
                        $eq: [{ $ifNull: ['$userDetails.company_id', ''] }, ''],
                      },
                    ],
                  },
                  then: {
                    $concat: [
                      {
                        $ifNull: [
                          {
                            $trim: { input: '$supplierInvitationDetails.name' },
                          },
                          '',
                        ],
                      },
                      '_',
                      {
                        $ifNull: [
                          {
                            $trim: {
                              input: '$supplierInvitationDetails.email',
                            },
                          },
                          '',
                        ],
                      },
                    ],
                  },
                  else: {
                    $convert: {
                      input: '$userDetails.company_id',
                      to: 'objectId',
                      onError: null,
                    },
                  },
                },
              },
              else: {
                $convert: {
                  input: '$company_id',
                  to: 'objectId',
                  onError: null,
                },
              },
            },
          },
        },
      },
      {
        $lookup: {
          from: Company.collection.name,
          localField: 'supplier_id',
          foreignField: '_id',
          as: 'tempCompany',
          pipeline: [
            {
              $project: {
                draft: 0,
                credit: 0,
                finicity: 0,
                address: 0,
                bankAccounts: 0,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          company: {
            $cond: {
              if: { $gt: [{ $size: '$tempCompany' }, 0] },
              then: { $arrayElemAt: ['$tempCompany', 0] },
              else: {
                name: '$supplierInvitationDetails.name',
                phone: '$supplierInvitationDetails.phone',
                email: '$supplierInvitationDetails.email',
                isInvited: true,
              },
            },
          },
        },
      },
    )

    if (Types.ObjectId.isValid(supplierId) && !supplierIsInvited) {
      pipeline.push({
        $match: {
          supplier_id: new Types.ObjectId(supplierId),
        },
      })
    } else {
      pipeline.push({
        $match: {
          supplier_id: supplierId.trim(),
        },
      })
    }
  }

  pipeline.push(
    {
      $lookup: {
        from: Operation.collection.name,
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          { $match: { $expr: { $eq: ['$owner_id', '$$invoice_id'] } } },
          {
            $match: {
              $expr: {
                $or: [
                  {
                    $and: [
                      {
                        $in: [
                          '$status',
                          [
                            OPERATION_STATUS.PLACED,
                            OPERATION_STATUS.PROCESSING,
                            OPERATION_STATUS.SUCCESS,
                            OPERATION_STATUS.FAIL,
                          ],
                        ],
                      },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.PAYMENT] },
                    ],
                  },
                  {
                    $and: [
                      { $eq: ['$status', OPERATION_STATUS.SUCCESS] },
                      { $eq: ['$type', OPERATION_TYPES.INVOICE.REFUND] },
                    ],
                  },
                ],
              },
            },
          },
          { $limit: 1 },
        ],
        as: 'operation',
      },
    },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        let: { invoice_id: { $toString: '$_id' } },
        pipeline: [
          {
            $addFields: {
              ids: {
                $cond: {
                  if: {
                    $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                  },
                  then: '$invoiceDetails.invoiceId',
                  else: ['$invoiceDetails.invoiceId'],
                },
              },
            },
          },
          { $match: { $expr: { $in: ['$$invoice_id', '$ids'] } } },
          { $sort: { createdAt: -1 } },
          { $limit: 1 },
          { $project: { status: 1, creditApplicationId: 1 } },
        ],
        as: 'loanApp',
      },
    },
    { $unwind: { path: '$loanApp', preserveNullAndEmptyArrays: true } },
    {
      $addFields: {
        operation: { $arrayElemAt: ['$operation', 0] },
        lateFee: {
          $cond: {
            if: {
              $eq: [
                '$company.settings.arAdvance.isLateInterestChargedToMerchant',
                false,
              ],
            },
            then: {
              $ifNull: ['$paymentDetails.fees', 0],
            },
            else: 0,
          },
        },
      },
    },
    {
      $addFields: {
        totalPaidAmount: {
          $cond: [
            { $ifNull: ['$operation.paidAmount', false] },
            '$operation.paidAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
        totalProcessingAmount: {
          $cond: [
            { $ifNull: ['$operation.processingAmount', false] },
            '$operation.processingAmount',
            {
              $cond: [
                { $eq: ['$operation.status', OPERATION_STATUS.PROCESSING] },
                '$operation.amount',
                0,
              ],
            },
          ],
        },
      },
    },
    {
      $addFields: {
        totalRemainingAmount: {
          $round: [
            {
              $subtract: [
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$operation.amount', '$total_amount'] },
                        { $ifNull: ['$lateFee', 0] },
                        { $ifNull: ['$paymentDetails.customerFee', 0] },
                      ],
                    },
                    2,
                  ],
                },
                {
                  $round: [
                    {
                      $add: [
                        { $ifNull: ['$totalPaidAmount', 0] },
                        { $ifNull: ['$totalProcessingAmount', 0] },
                      ],
                    },
                    2,
                  ],
                },
              ],
            },
            2,
          ],
        },
      },
    },
    {
      $addFields: {
        isActiveInvoice: {
          $and: [
            {
              $or: [
                {
                  $not: {
                    $in: [
                      '$status',
                      [
                        invoiceStatus.dismissed,
                        invoiceStatus.cancelled,
                        invoiceStatus.draft,
                      ],
                    ],
                  },
                },
                {
                  $and: [
                    { $eq: ['$status', invoiceStatus.draft] },
                    { $gt: ['$supplierInvitationDetails.name', ''] },
                    { approved: false },
                  ],
                },
              ],
            },
            {
              $or: [
                { $eq: ['$loanApp', null] },
                {
                  $not: {
                    $in: [
                      '$loanApp.status',
                      [
                        ...PROCESSING_STATUSES,
                        LOAN_APPLICATION_STATUS.APPROVED,
                      ],
                    ],
                  },
                },
              ],
            },
            {
              $or: [
                { $eq: ['$operation', null] },
                { $eq: ['$operation.status', null] },
                {
                  $and: [
                    { $ne: ['$operation.status', OPERATION_STATUS.SUCCESS] },
                    { $gt: ['$totalRemainingAmount', 0] },
                  ],
                },
              ],
            },
            {
              $or: [
                {
                  $and: [
                    { $ne: [{ $ifNull: ['$expiration_date', null] }, null] },
                    { $lte: [cstNow, '$expiration_date'] },
                  ],
                },
                { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
              ],
            },
            /*{
              $ne: ['$type', EInvoiceType.QUOTE],
            },*/
          ],
        },
      },
    },
    {
      $group: {
        _id: null,
        totalDueInvoices: {
          $sum: { $cond: [{ $eq: ['$isActiveInvoice', true] }, 1, 0] },
        },
        totalDueSum: {
          $sum: {
            $cond: [
              { $eq: ['$isActiveInvoice', true] },
              '$totalRemainingAmount',
              0,
            ],
          },
        },
      },
    },
    {
      $project: {
        _id: 0,
        totalDueInvoices: 1,
        totalDueSum: 1,
      },
    },
  )

  const [invoiceAggregation] = await Invoice.aggregate(pipeline)

  return invoiceAggregation
}
./packages/server/src/controllers/payables.invoice.controller.ts:                      { $eq: ['$$company_id', null] },
./packages/server/src/controllers/payables.invoice.controller.ts:                { $eq: ['$company_id', null] },
./packages/server/src/controllers/payables.invoice.controller.ts:                    { $eq: ['$supplierInvitationDetails.email', null] },
./packages/server/src/controllers/payables.invoice.controller.ts:                    { $eq: [{ $ifNull: ['$userDetails', null] }, null] },
./packages/server/src/controllers/tradeCredit.draws.controller.ts:              { 'invoice.company_id': { $eq: null } },
./packages/server/src/controllers/tradeCredit.draws.controller.ts:              { 'invoice.company_id': { $eq: null } },
./packages/server/src/controllers/vendors.controller.ts:                      { $eq: ['$$company_id', null] },
./packages/server/src/controllers/vendors.controller.ts:                { $eq: ['$company_id', null] },
./packages/server/src/controllers/vendors.controller.ts:                    { $eq: ['$supplierInvitationDetails.email', null] },
./packages/server/src/controllers/vendors.controller.ts:                    { $eq: [{ $ifNull: ['$userDetails', null] }, null] },
./packages/server/src/controllers/vendors.controller.ts:                { $eq: ['$loanApp', null] },
./packages/server/src/controllers/vendors.controller.ts:                { $eq: ['$operation', null] },
./packages/server/src/controllers/vendors.controller.ts:                { $eq: ['$operation.status', null] },
./packages/server/src/controllers/vendors.controller.ts:                { $eq: [{ $ifNull: ['$expiration_date', null] }, null] },
./packages/server/src/controllers/vendors.controller.ts:                    { $eq: ['$operation', null] },
./packages/server/src/routes/v1/admin.route/loan/payments.ts:                { $eq: ['$lastPaymentDate', null] },
./packages/server/src/routes/v1/company.route/bankAccounts.ts:            $eq: ['$finicity.accountId', null],
./packages/server/src/routes/v1/invoices.route/getActionRequiredInvoices.ts:                        { isDeactivated: { $exists: true, $eq: null } },
./packages/server/src/routes/v1/invoices.route/invoice.ts:                    { isDeactivated: { $exists: true, $eq: null } },
./packages/server/src/routes/v1/settlements.route/checkSettlementsExist.ts:          { paymentDetails: { $eq: null } },
./packages/server/src/routes/v1/settlements.route/checkSettlementsExist.ts:                  { 'paymentDetails.paymentType': { $eq: null } },
./packages/server/src/routes/v1/settlements.route/tradeCreditSettlements.ts:            { paymentDetails: { $eq: null } },
./packages/server/src/routes/v1/settlements.route/tradeCreditSettlements.ts:                    { 'paymentDetails.paymentType': { $eq: null } },
./packages/server/src/routes/v1/supplier.route/customerAdditionalInfo.ts:                    { isDeactivated: { $exists: true, $eq: null } },
./packages/services/finicity/importTransactions.ts:      { 'finicity.lastImport': mongoose.trusted({ $eq: null }) },
./packages/services/hubspot/getData/loanApplications.ts:                          { $eq: ['$lastInvoice.company_id', null] },
