import { PipelineStage, Types } from 'mongoose'
import Middlewares from './middlewares'
import { ControllerItem } from 'src/routes/controllerItem'
import { BankAccount, CustomerAccount, User } from '@linqpal/common-backend'
import { getBillingContacts } from './getBillingContacts'

export const customerAdditionalInfo = {
  middlewares: {
    pre: [...Middlewares.pre],
  },
  get: async (req, res) => {
    const customerAccountId = req.query.id as string

    if (req.company?.settings?.supplierCanPay) {
      const pipeline: PipelineStage[] = [
        {
          $match: {
            _id: new Types.ObjectId(customerAccountId),
            company_id: req.company!.id,
          },
        },
        {
          $lookup: {
            from: BankAccount.collection.name,
            localField: 'bankAccounts',
            foreignField: '_id',
            as: 'bankAccounts',
            pipeline: [
              {
                $match: {
                  $or: [
                    { isDeactivated: { $exists: true, $eq: false } },
                    { isDeactivated: { $exists: false } },
                    { isDeactivated: { $exists: true, $in: [null, undefined] } },
                  ],
                },
              },
              {
                $project: {
                  name: 1,
                  accountholderName: { $ifNull: ['$accountholderName', ''] },
                  routingNumber: 1,
                  accountNumber: '$accountNumber.display',
                  isManualEntry: 1,
                  paymentMethodType: 1,
                  'cardMetadata.accountId': 1,
                  'cardMetadata.type': 1,
                  billingAddress: 1,
                  isPrimary: 1,
                  accountType: 1,
                  finicity: 1,
                  status: 1,
                  _id: 1,
                  id: '$_id',
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: User.collection.name,
            as: 'salesRepUserInfo',
            let: {
              salesRepObjectId: {
                $convert: {
                  input: '$salesRepId',
                  to: 'objectId',
                  onError: null,
                },
              },
            },
            pipeline: [
              { $match: { $expr: { $eq: ['$_id', '$$salesRepObjectId'] } } },
              {
                $project: {
                  fullName: { $concat: ['$firstName', ' ', '$lastName'] },
                  _id: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$salesRepUserInfo',
            preserveNullAndEmptyArrays: true,
          },
        },
      ]

      const result = await CustomerAccount.aggregate([...pipeline])
      const billingContacts = await getBillingContacts(
        customerAccountId as string,
      )
      const account = result[0]

      res.send({
        bankAccounts: account.bankAccounts,
        salesRepUserInfo: account.salesRepUserInfo,
        billingContacts,
      })
      return
    }

    res.send({ bankAccounts: undefined })
  },
} as ControllerItem
