import { acceptChallenge, authRequired } from '../../../services/auth.service'
import { BankAccount, Company, plaidService } from '@linqpal/common-backend'
import { ControllerItem } from 'src/routes/controllerItem'
import { PipelineStage } from 'mongoose'
import { BankConnectionType } from '@linqpal/models/src/dictionaries'
import { v4 } from 'uuid'

export default {
  middlewares: { pre: [acceptChallenge, authRequired(false)] },
  get: async (req, res, next) => {
    if (!req.user) {
      res.locals.result = { bankAccounts: [] }
      return next()
    }

    const requestId = v4()
    const companyId = req.company!.id

    const company = await Company.findOne({ _id: companyId }).populate(
      'bankAccounts',
    )

    const bankAccountIds = company?.bankAccounts?.map((bankAccount) =>
      bankAccount._id.valueOf(),
    )

    await plaidService.postHealthCheckBankAccount({
      userId: req.user.id,
      bankAccountIds: bankAccountIds as string[],
      companyId: companyId,
      correlationId: `${requestId}-${req.user.id}-NodeJS`,
    })

    const pipeline: PipelineStage[] = [
      {
        $match: {
          _id: {
            $in: company?.bankAccounts,
          },
          isDeactivated: {
            $ne: true,
          },
        },
      },
      {
        $match: {
          $or: [
            {
              paymentMethodType: 'card',
            },
            {
              $and: [
                {
                  paymentMethodType: 'bank',
                },
                {
                  accountType: {
                    $in: ['savings', 'checking'],
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $addFields: {
          isPlaidConnection: { $ifNull: ['$plaid', false] },
        },
      },
      {
        $project: {
          name: {
            $cond: [
              { $eq: ['$isPlaidConnection', false] },
              '$name',
              '$accountName',
            ],
          },
          connectionType: {
            $cond: [
              { $eq: ['$isPlaidConnection', false] },
              BankConnectionType.FINICITY,
              BankConnectionType.PLAID,
            ],
          },
          plaid: {
            status: 1,
          },
          accountholderName: { $ifNull: ['$accountholderName', ''] },
          routingNumber: 1,
          accountNumber: '$accountNumber.display',
          isManualEntry: 1,
          paymentMethodType: 1,
          cardMetadata: {
            accountId: 1,
            type: 1,
            network: 1,
            isRegulated: 1,
            lastFour: 1,
            nameFI: '$cardMetadata.response.queryCardResponse.card.nameFI',
          },
          billingAddress: 1,
          isPrimary: 1,
          isPrimaryForCredit: { $ifNull: ['$isPrimaryForCredit', false] },
          isPrimaryForIHCAutoPay: {
            $ifNull: ['$isPrimaryForIHCAutoPay', false],
          },
          accountType: 1,
          finicity: 1,
          status: 1,
          _id: 1,
          id: '$_id',
        },
      },
    ]
    const { status, type = 'all' } = req.query
    if (status) {
      pipeline.push({
        $match: {
          $expr: {
            $eq: ['$status', status],
          },
        },
      })
    }
    if (type === 'finicity') {
      pipeline.push({
        $match: {
          $expr: {
            $ne: ['$finicity.accountId', null],
          },
        },
      })
    } else if (type === 'manual') {
      pipeline.push({
        $match: {
          $expr: {
            $or: [
              { $eq: ['$finicity.accountId', null] },
              { $eq: ['$finicity.accountId', undefined] },
            ],
          },
        },
      })
    }
    const bankAccounts = await BankAccount.aggregate(pipeline)
    res.locals.result = { bankAccounts }
    next()
  },
} as ControllerItem
