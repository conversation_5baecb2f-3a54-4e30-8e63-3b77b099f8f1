import {
  BankAccount,
  Company,
  CustomerAccount,
  Invoice,
  User,
  UserRole,
} from '@linqpal/common-backend'
import { authRequired } from '../../../services/auth.service'
import moment from 'moment'
import { dictionaries, EInvoiceType, InvoicePaymentType } from '@linqpal/models'
import { ControllerItem } from 'src/routes/controllerItem'
import { PipelineStage } from 'mongoose'

const { invoiceSchemaStatus } = dictionaries

export default {
  middlewares: { pre: [authRequired()] },
  get: async (req, res) => {
    const { type, paymentType } = req.query
    const format = '%m/%d/%Y'
    const timezone = moment().format('Z')
    const query = {}
    if (paymentType) {
      if (Array.isArray(paymentType)) {
        Object.assign(query, {
          'paymentDetails.paymentType': { $in: paymentType },
        })
      } else {
        Object.assign(query, { 'paymentDetails.paymentType': paymentType })
      }
    } else {
      let arAdvanceIsEnabled
      if (req.company?.settings) {
        arAdvanceIsEnabled = req.company.settings?.arAdvance?.isEnabled
      } else {
        const company = await Company.findById(
          req.company!._id.toString(),
        ).exec()

        arAdvanceIsEnabled = company?.settings?.arAdvance?.isEnabled
      }
      if (arAdvanceIsEnabled) {
        Object.assign(query, {
          $or: [
            {
              'paymentDetails.paymentType': {
                $ne: InvoicePaymentType.FACTORING,
              },
            },
            {
              paymentDetails: null,
            },
          ],
        })
      }
    }

    const pipeline: PipelineStage[] = [
      {
        $match: {
          $and: [
            { 'supplierInvitationDetails.email': { $exists: true } },
            { 'supplierInvitationDetails.email': req.user!.email },
            { 'supplierInvitationDetails.paymentMethodId': { $exists: true } },
            { 'supplierInvitationDetails.paymentMethodId': { $ne: 'credit' } },
          ],
          status: invoiceSchemaStatus.draft,
          type: type ?? EInvoiceType.INVOICE,
          ...query,
          isDeleted: { $ne: true },
        },
      },
      {
        $project: {
          _id: { $toString: '$_id' },
          payer_id: {
            $convert: {
              input: '$payer_id',
              to: 'objectId',
              onError: null,
            },
          },
          invoice_document: 1,
          company_id: 1,
          document_name: 1,
          invoice_date: 1,
          invoice_due_date: 1,
          expiration_date: 1,
          address: 1,
          supplierInvitationDetails: 1,
          unitNumber: 1,
          city: 1,
          state: 1,
          zip: 1,
          addressType: 1,
          customer_user_id: 1,
          invoice_number: 1,
          total_amount: 1,
          material_subtotal: 1,
          tax_amount: 1,
          material_description: 1,
          transaction_ids: 1,
          note: 1,
          createdAt: 1,
          status: 1,
          createdBy: 1,
          connector: 1,
        },
      },
      {
        $lookup: {
          from: Company.collection.name,
          as: 'customer',
          let: {
            payerId: '$payer_id',
            contractorId: {
              $convert: {
                input: '$supplierInvitationDetails.userId',
                to: 'objectId',
                onError: null,
              },
            },
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$payerId'],
                },
              },
            },
            {
              $lookup: {
                from: UserRole.collection.name,
                as: 'role',
                let: {
                  companyId: {
                    $toString: '$$payerId',
                  },
                  contractorId: '$$contractorId',
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ['$company_id', '$$companyId'],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: User.collection.name,
                      as: 'user',
                      let: {
                        sub: '$sub',
                        userId: '$$contractorId',
                      },
                      pipeline: [
                        {
                          $match: {
                            $and: [
                              {
                                $expr: {
                                  $eq: ['$sub', '$$sub'],
                                },
                              },
                              {
                                $expr: {
                                  $eq: ['$_id', '$$userId'],
                                },
                              },
                            ],
                          },
                        },
                        {
                          $project: {
                            first_name: '$firstName',
                            last_name: '$lastName',
                            phone: 1,
                            id: { $toString: '$_id' },
                          },
                        },
                      ],
                    },
                  },
                  {
                    $unwind: {
                      path: '$user',
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $match: {
                      user: {
                        $ne: null,
                      },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$role',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                contact: {
                  $concat: [
                    '$role.user.first_name',
                    ' ',
                    '$role.user.last_name',
                  ],
                },
                phone: '$role.user.phone',
                name: 1,
              },
            },
          ],
        },
      },
      { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
      {
        $project: {
          invoice_document: 1,
          document_name: 1,
          invoice_date: {
            $dateToString: { date: '$invoice_date', format, timezone },
          },
          invoice_due_date: {
            $dateToString: { date: '$invoice_due_date', format, timezone },
          },
          expiration_date: {
            $dateToString: { date: '$expiration_date', format, timezone },
          },
          address: {
            $concat: [
              '$address',
              {
                $cond: [
                  { $lte: ['$city', ''] },
                  '',
                  { $concat: [', ', '$city'] },
                ],
              },
              {
                $cond: [
                  { $lte: ['$state', ''] },
                  '',
                  { $concat: [', ', '$state'] },
                ],
              },
              {
                $cond: [
                  { $lte: ['$zip', ''] },
                  '',
                  { $concat: [', ', '$zip'] },
                ],
              },
            ],
          },
          addressType: 1,
          customer_account_id: 1,
          customer_user_id: 1,
          invoice_number: 1,
          total_amount: 1,
          material_subtotal: 1,
          tax_amount: 1,
          material_description: 1,
          note: 1,
          createdAt: 1,
          company_id: 1,
          customer: 1,
          operation: 1,
          status: { $cond: [{ $eq: ['$company_id', ''] }, 'PLACED', 'DRAFT'] },
          createdBy: 1,
          connector: 1,
        },
      },
    ]

    const pipelineDraft: any[] = [
      {
        $match: {
          company_id: req.company!._id.toString(),
          status: invoiceSchemaStatus.draft,
          type: type ?? EInvoiceType.INVOICE,
          ...query,
        },
      },
      { $match: { isDeleted: { $ne: true } } },
      {
        $match: {
          $and: [
            {
              $or: [
                {
                  company_id: {
                    $ne: '',
                  },
                },
              ],
            },
          ],
        },
      },
      {
        $project: {
          _id: { $toString: '$_id' },
          company_id: 1,
          customer_account_id: {
            $convert: {
              input: '$customer_account_id',
              to: 'objectId',
              onError: null,
            },
          },
          invoice_document: 1,
          document_name: 1,
          invoice_date: 1,
          invoice_due_date: 1,
          expiration_date: { $ifNull: ['$expiration_date', null] },
          address: 1,
          unitNumber: 1,
          city: 1,
          state: 1,
          seen: { $ifNull: ['$seen', false] },
          zip: 1,
          addressType: 1,
          customer_user_id: 1,
          invoice_number: 1,
          total_amount: 1,
          material_subtotal: 1,
          tax_amount: 1,
          material_description: 1,
          transaction_ids: 1,
          note: 1,
          createdAt: 1,
          payer_id: 1,
          status: 1,
          type: 1,
          createdBy: 1,
          connector: 1,
        },
      },
      {
        $lookup: {
          from: CustomerAccount.collection.name,
          as: 'customer',
          let: {
            customer_account_id: '$customer_account_id',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $eq: ['$_id', '$$customer_account_id'],
                },
                isDeleted: { $ne: true },
              },
            },
            {
              $lookup: {
                from: User.collection.name,
                as: 'user',
                let: {
                  phone: '$phone',
                },
                pipeline: [
                  {
                    $match: {
                      $expr: {
                        $eq: ['$login', '$$phone'],
                      },
                    },
                  },
                  {
                    $lookup: {
                      from: UserRole.collection.name,
                      as: 'role',
                      let: {
                        sub: '$sub',
                      },
                      pipeline: [
                        {
                          $match: {
                            $expr: {
                              $eq: ['$sub', '$$sub'],
                            },
                          },
                        },
                        {
                          $lookup: {
                            from: Company.collection.name,
                            as: 'company',
                            let: {
                              company_id: {
                                $toObjectId: '$company_id',
                              },
                            },
                            pipeline: [
                              {
                                $match: {
                                  $expr: {
                                    $eq: ['$_id', '$$company_id'],
                                  },
                                },
                              },
                              {
                                $project: {
                                  name: 1,
                                },
                              },
                            ],
                          },
                        },
                        {
                          $unwind: {
                            path: '$company',
                            preserveNullAndEmptyArrays: true,
                          },
                        },
                        {
                          $project: {
                            company: 1,
                          },
                        },
                      ],
                    },
                  },
                  {
                    $unwind: {
                      path: '$role',
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $project: {
                      role: 1,
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$user',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $lookup: {
                from: BankAccount.collection.name,
                localField: 'bankAccounts',
                foreignField: '_id',
                as: 'bankAccounts',
                pipeline: [
                  {
                    $match: {
                      $or: [
                        { isDeactivated: { $exists: true, $eq: false } },
                        { isDeactivated: { $exists: false } },
                        { isDeactivated: { $exists: true, $in: [null, undefined] } },
                      ],
                    },
                  },
                  {
                    $project: {
                      name: 1,
                      accountholderName: {
                        $ifNull: ['$accountholderName', ''],
                      },
                      routingNumber: 1,
                      accountNumber: '$accountNumber.display',
                      isManualEntry: 1,
                      paymentMethodType: 1,
                      'cardMetadata.accountId': 1,
                      'cardMetadata.type': 1,
                      billingAddress: 1,
                      isPrimary: 1,
                      accountType: 1,
                      finicity: 1,
                      status: 1,
                      _id: 1,
                      id: '$_id',
                    },
                  },
                ],
              },
            },
          ],
        },
      },
      { $unwind: { path: '$customer', preserveNullAndEmptyArrays: true } },
      {
        $set: {
          'customer.name': {
            $cond: [
              {
                $and: [
                  {
                    $ifNull: ['$customer.user.role.company.name', false],
                  },
                  {
                    $ne: ['$customer.user.role.company.name', ''],
                  },
                ],
              },
              '$customer.user.role.company.name',
              '$customer.name',
            ],
          },
        },
      },
      {
        $project: {
          invoice_document: 1,
          document_name: 1,
          invoice_date: {
            $dateToString: { date: '$invoice_date', format, timezone },
          },
          invoice_due_date: {
            $dateToString: { date: '$invoice_due_date', format, timezone },
          },
          expiration_date: {
            $dateToString: { date: '$expiration_date', format, timezone },
          },
          address: {
            $concat: [
              '$address',
              {
                $cond: [
                  { $lte: ['$city', ''] },
                  '',
                  { $concat: [', ', '$city'] },
                ],
              },
              {
                $cond: [
                  { $lte: ['$state', ''] },
                  '',
                  { $concat: [', ', '$state'] },
                ],
              },
              {
                $cond: [
                  { $lte: ['$zip', ''] },
                  '',
                  { $concat: [', ', '$zip'] },
                ],
              },
            ],
          },
          addressType: 1,
          customer_account_id: 1,
          customer_user_id: 1,
          invoice_number: 1,
          total_amount: 1,
          material_subtotal: 1,
          tax_amount: 1,
          material_description: 1,
          note: 1,
          createdAt: 1,
          customer: 1,
          payer_id: 1,
          seen: 1,
          type: 1,
          status: 'DRAFT',
          company_id: 1,
          createdBy: 1,
          connector: 1,
        },
      },
    ]

    const [items1, items2] = await Promise.all([
      Invoice.aggregate(pipeline),
      Invoice.aggregate(pipelineDraft),
    ])
    const items = [...items1, ...items2].map((inv) => {
      return {
        id: inv._id,
        ...inv,
        customer: inv.customer
          ? {
              id: inv.customer._id,
              ...inv.customer,
            }
          : null,
      }
    })

    res.send({
      invoices: items,
      items,
      totalCount: items?.length,
    })
  },
} as ControllerItem
