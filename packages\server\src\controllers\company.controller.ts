import {
  AgreementService,
  BankAccount,
  Company,
  FactoringService,
  GlobalLogger,
  LMS,
  LoanApplication,
  Settings,
} from '@linqpal/common-backend'
import {
  ICompany,
  ILoanApplication,
  IUser,
} from '@linqpal/common-backend/src/models/types'
import {
  BlueTapeCreditInfo,
  dictionaries,
  exceptions,
  InHouseCreditInfo,
  TradeCreditDrawsInfo,
} from '@linqpal/models'
import { Request } from 'express'
import 'moment-business-days'
import {
  APPROVED_STATUSES,
  DECISION_STEPS,
  LmsCreditStatus,
  LOAN_APPLICATION_STATUS,
  PROCESSING_STATUSES,
} from '../../../models/src/dictionaries'
import {
  AgreementType,
  ILoanAgreementCreateInfo,
  ILoanAgreementIntegrationCreateInfo,
} from '@linqpal/common-backend/src/services/agreement/types'
import {
  LOAN_STATUS,
  UI_LOAN_STATUS,
  UI_LOAN_STATUSES_PRIORITY,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import TradeCreditService from '@linqpal/common-backend/src/services/tradeCredit/tradeCredit.service'
import mongoose, { PipelineStage } from 'mongoose'
import { DrawsInactiveStatusType } from '@linqpal/models/src/dictionaries/tradeCredit'
import { CreditApplicationType } from '@linqpal/models/src/dictionaries/creditApplicationType'
import { PricingProduct } from '@linqpal/models/src/dictionaries/pricingProduct'
import { TradeCreditStatus } from '@linqpal/models/src/dictionaries/TradeCreditStatus'
import { ICustomerSettings } from '@linqpal/models/src/types/routes'
import { getTotalInvoicesDue } from './invoicesVendorStats.controller'
import moment from 'moment'

const { CLOSED, CANCELED } = LOAN_STATUS
const {
  PAST_DUE,
  PAST_DUE_LATE,
  PAST_DUE_LATE_AND_INTEREST,
  PAST_DUE_PENALTY,
} = UI_LOAN_STATUS

export async function makeAgreement(req: Request) {
  const { id, isAdmin } = req.query
  const application = await LoanApplication.findById(id)
  if (!application)
    throw new exceptions.LogicalError('No loan application found')

  let agreement =
    await AgreementService.getLatestAgreementByLoanApplicationIdAndType(
      application.id,
    )

  if (agreement == null) {
    const user = isAdmin && isAdmin === 'true' ? null : req.user
    agreement = await AgreementService.createAgreementForLoanApplication(
      application.id,
      true,
      user,
    )
  }
  return { url: agreement.url, fileName: agreement.fileName, result: 'ok' }
}

export async function previewAgreement(
  details: {
    agreement_type: string
    paymentPlan?: any
    invoiceIds?: string[]
    companyId: string
  },
  user: IUser | null | undefined = null,
) {
  const { agreement_type, paymentPlan, invoiceIds, companyId } = details
  const templateType =
    agreement_type === 'master'
      ? AgreementType.MASTER_AGREEMENT
      : AgreementType.BNPL_AGREEMENT
  const plan = paymentPlan?._id.toString()
  let invIds: string[] = []
  if (templateType !== AgreementType.MASTER_AGREEMENT && invoiceIds) {
    invIds = Array.isArray(invoiceIds) ? invoiceIds : [invoiceIds]
  }
  const data: ILoanAgreementCreateInfo = {
    company_id: companyId,
    invoiceId: invIds,
    paymentPlan: plan,
  }
  const agreement =
    await AgreementService.createPreviewAgreementWithoutLoanApplication(
      data,
      templateType,
      false,
      user,
    )
  return { url: agreement.url, fileName: agreement.fileName, result: 'ok' }
}

export async function previewIntegrationAgreement(
  details: {
    paymentPlan?: any
    totalAmount: number
    companyId: string
    supplierCompanyId: string
  },
  user: IUser | null | undefined = null,
) {
  const {
    paymentPlan,
    totalAmount: loanAmount,
    companyId,
    supplierCompanyId,
  } = details
  const plan = paymentPlan?.name || paymentPlan?._id.toString()

  const data: ILoanAgreementIntegrationCreateInfo = {
    companyId,
    paymentPlan: plan,
    supplierCompanyId,
    loanAmount,
  }
  const agreement =
    await AgreementService.createIntegrationPreviewBnplAgreement(data, user)
  return { url: agreement.url, fileName: agreement.fileName, result: 'ok' }
}

export async function viewAgreement(
  req: Request,
): Promise<{ url?: string; fileName?: string; result: string }> {
  const { id } = req.query
  const application = await LoanApplication.findById(id)
  if (!application)
    throw new exceptions.LogicalError('No loan application found')

  let agreement =
    await AgreementService.getLatestAgreementByLoanApplicationIdAndType(
      application.id,
    )

  if (agreement == null) {
    agreement = await AgreementService.createAgreementForLoanApplication(
      application.id,
      false,
      req.user,
    )
  }
  return { url: agreement.url, fileName: agreement.fileName, result: 'ok' }
}

export async function tryGetMasterAgreement(companyId: string) {
  if (companyId) {
    const agreement =
      await AgreementService.getLatestMasterAgreementByCompanyId(companyId)

    if (agreement) {
      return {
        url: agreement.url,
        fileName: agreement.fileName,
        semanticVersion: agreement.semanticVersion,
        createdAt: agreement.createdAt,
        result: 'ok',
      }
    }
  }

  return { result: 'ok' }
}

export const getCreditAppStatus = (
  status: dictionaries.LoanApplicationStatuses | undefined,
  decisionDate: Date | string | undefined,
) => {
  if (status) {
    if (status === LOAN_APPLICATION_STATUS.REJECTED && decisionDate) {
      const threeMonthsAgo = moment().subtract(3, 'months')
      const decisionMoment = moment(decisionDate)

      if (decisionMoment.isBefore(threeMonthsAgo)) {
        return ''
      }
    }

    if (PROCESSING_STATUSES.includes(status))
      return LOAN_APPLICATION_STATUS.PROCESSING
    else if (APPROVED_STATUSES.includes(status))
      return LOAN_APPLICATION_STATUS.APPROVED
    else return status
  } else {
    return ''
  }
}

export function pricingPackageRequired(company: ICompany) {
  const cardPricingPackageId = company?.settings?.cardPricingPackageId
  const loanPricingPackageId = company?.settings?.loanPricingPackageId

  const packagesChosen = !!cardPricingPackageId && !!loanPricingPackageId

  return !packagesChosen
}

export const tradeCreditDrawsInfo = async (
  company_id: string,
): Promise<TradeCreditDrawsInfo> => {
  const company = await Company.aggregate([
    {
      $match: {
        _id: company_id,
      },
    },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loans',
        let: { id: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $eq: ['$company_id', '$$id'] },
                  { $ne: ['$lms_id', null] },
                  { $ne: ['$lms_id', 0] },
                  { $ne: ['$lms_id', '0'] },
                  { $ne: ['$invoiceDetails.invoiceId', null] },
                ],
              },
              $or: [
                {
                  status: {
                    $in: [
                      LOAN_APPLICATION_STATUS.APPROVED,
                      LOAN_APPLICATION_STATUS.CLOSED,
                      LOAN_APPLICATION_STATUS.EXPIRED,
                    ],
                  },
                },
                // Auto Trade Credit draws waiting for disbursement
                // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
                {
                  $and: [
                    { status: LOAN_APPLICATION_STATUS.PROCESSING },
                    { lms_id: { $ne: null } },
                    { 'metadata.repayment.autoTradeCreditEnabled': true },
                    {
                      'progress.step': DECISION_STEPS.PENDING_MERCHANT_TRANSFER,
                    },
                  ],
                },
              ],
              lms_id: { $exists: true },
              issueDate: { $ne: null },
            },
          },
          {
            $sort: {
              createdAt: 1,
              updatedAt: 1,
            },
          },
        ],
      },
    },
  ])

  const approvedLoans: ILoanApplication[] = company[0].loans

  const lmsIds = approvedLoans.map((l) => ({ id: l.lms_id }))
  const lmsLoans = await LMS.getLoansByIds({ ids: lmsIds })

  const activeDraws = lmsLoans.filter(
    (l) => ![CLOSED, CANCELED].includes(l.status as DrawsInactiveStatusType),
  )
  const activeDrawsCount = activeDraws.length

  const inactiveDrawsCount = lmsLoans.filter((l) =>
    [CLOSED, CANCELED].includes(l.status as DrawsInactiveStatusType),
  ).length

  const pastDueDraws = activeDraws.filter(
    (l) =>
      l.currentStatus &&
      [
        PAST_DUE,
        PAST_DUE_LATE,
        PAST_DUE_LATE_AND_INTEREST,
        PAST_DUE_PENALTY,
      ].includes(l.currentStatus),
  )

  let pastDueWorstStatus = pastDueDraws[0]?.currentStatus || ''
  if (pastDueDraws.length > 1) {
    pastDueDraws.slice(1).forEach((draw) => {
      if (
        draw.currentStatus &&
        UI_LOAN_STATUSES_PRIORITY[draw.currentStatus] >
          UI_LOAN_STATUSES_PRIORITY[pastDueWorstStatus]
      ) {
        pastDueWorstStatus = draw.currentStatus
      }
    })
  }
  const pastDueDrawsCount = pastDueDraws.length

  return {
    activeDrawsCount,
    inactiveDrawsCount,
    pastDueDrawsCount,
    pastDueWorstStatus,
  }
}

export const blueTapeCreditInfo = async (
  company: ICompany | null,
): Promise<BlueTapeCreditInfo> => {
  if (!company) {
    return {
      maximum_credit_amount: 0,
      available_credit: 0,
      held_amount: 0,
      outstanding_amount: 0,
      past_due_amount: 0,
      processing_amount: 0,
      account_status: '',
    }
  }
  const pipeline: PipelineStage[] = [
    {
      $match: {
        _id: company._id,
      },
    },
    {
      $lookup: {
        from: BankAccount.collection.name,
        localField: 'bankAccounts',
        foreignField: '_id',
        as: 'primaryAccount',
        let: {
          ids: { $ifNull: ['$bankAccounts', []] },
        },
        pipeline: [
          {
            $match: {
              $and: [
                {
                  $expr: {
                    $in: ['$_id', '$$ids'],
                  },
                },
                {
                  $expr: {
                    $in: ['$status', ['verified', 'manualverified']],
                  },
                },
                {
                  $or: [
                    { isDeactivated: { $exists: true, $eq: false } },
                    { isDeactivated: { $exists: false } },
                    { isDeactivated: { $exists: true, $in: [null, undefined] } },
                  ],
                },
                {
                  $or: [
                    { isPrimaryForCredit: true },
                    {
                      $and: [
                        { isPrimaryForCredit: { $exists: false } },
                        { isPrimary: true },
                      ],
                    },
                    {
                      $and: [
                        {
                          isPrimaryForCredit: { $exists: true, $in: [null, undefined] },
                        },
                        { isPrimary: true },
                      ],
                    },
                  ],
                },
              ],
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: LoanApplication.collection.name,
        as: 'loans',
        let: { id: { $toString: '$_id' } },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$company_id', '$$id'],
              },
            },
          },
          {
            $sort: {
              createdAt: 1,
              updatedAt: 1,
            },
          },
        ],
      },
    },
    { $set: { primaryAccount: { $last: '$primaryAccount' } } },
  ]

  const [result, tradeCreditPeriod] = await Promise.all([
    Company.aggregate([...pipeline]),
    Settings.findOne({ key: 'trade_credit_period' }),
  ])

  const companyDetails = result[0]
  const loans: ILoanApplication[] = companyDetails.loans

  const creditInfo = await getLoanInfo(company._id.toString())

  let { status: account_status } = TradeCreditService.calculateStatus(
    loans,
    companyDetails.primaryAccount || null,
    tradeCreditPeriod?.value || 90,
  )

  if (creditInfo?.status === LmsCreditStatus.OnHold) {
    account_status = TradeCreditStatus.AccountOnHold
  }

  return {
    account_status,
    available_credit: creditInfo?.available_credit ?? 0,
    held_amount: creditInfo?.held_amount ?? 0,
    outstanding_amount: creditInfo?.outstanding_amount ?? 0,
    past_due_amount: creditInfo?.past_due_amount ?? 0,
    processing_amount: creditInfo?.processing_amount ?? 0,
    maximum_credit_amount: creditInfo?.maximum_credit_amount ?? 0,
  }
}

export const inHouseCreditInfo = async (
  merchantId: string,
  companyId: string,
  customerAccountId: string,
  settings: ICustomerSettings,
): Promise<InHouseCreditInfo> => {
  if (!merchantId)
    throw new exceptions.LogicalError(`Vendor id was not provided.`)

  if (!mongoose.connection.db) {
    throw new Error('Mongoose connection is not established')
  }

  const creditApplication = await mongoose.connection.db
    .collection('creditApplications')
    .findOne({
      companyId,
      merchantId,
      type: PricingProduct.InHouseCredit,
    })
  if (!creditApplication?._id) {
    return {
      account_status: TradeCreditStatus.NotApplied,
      available_credit: 0,
      held_amount: 0,
      outstanding_amount: 0,
      past_due_amount: 0,
      processing_amount: 0,
      maximum_credit_amount: 0,
      total_invoices_due: 0,
      credit_terms: '',
    }
  }

  const credits = await LMS.getCreditCompanyInfo(companyId)

  const inHouseCredit = credits.find(
    (credit) =>
      credit.product === CreditApplicationType.InHouseCredit &&
      credit.merchantId === merchantId,
  )

  const { status: account_status } = FactoringService.calculateStatus(
    creditApplication as any,
    settings,
    inHouseCredit,
  )

  const activeInvoices = await getTotalInvoicesDue(
    customerAccountId,
    merchantId,
  )

  return {
    account_status,
    available_credit: inHouseCredit?.creditDetails?.availableCredit ?? 0,
    held_amount: inHouseCredit?.creditDetails?.creditHoldAmount ?? 0,
    outstanding_amount: inHouseCredit?.creditDetails?.outstandingCredit ?? 0,
    past_due_amount: inHouseCredit?.creditDetails?.lateAmount ?? 0,
    processing_amount: inHouseCredit?.creditDetails?.processingAmount ?? 0,
    maximum_credit_amount: inHouseCredit?.creditLimit ?? 0,
    total_invoices_due: activeInvoices?.totalDueSum ?? 0,
    credit_terms: '',
  }
}

const getLoanInfo = async (companyId: string) => {
  const loanApplication = await LoanApplication.findOne({
    company_id: companyId,
    status: mongoose.trusted({
      $in: [LOAN_APPLICATION_STATUS.APPROVED, LOAN_APPLICATION_STATUS.CLOSED],
    }),
  })

  if (!loanApplication) {
    GlobalLogger.info(
      { company_id: companyId },
      'Loan Application for requesting credit info is not found',
    )

    return null
  }

  if (!loanApplication.creditApplicationId) {
    GlobalLogger.info(
      { loanApplication: loanApplication.toJSON() },
      `Credit application id for requesting credit info is not found`,
    )

    return null
  }

  const credit = await LMS.getCreditInfoByCreditApplicationIdAndCompanyId(
    loanApplication.creditApplicationId,
    loanApplication.company_id,
  )

  if (!credit) {
    GlobalLogger.info(
      { loanApplication: loanApplication.toJSON() },
      `Credit info for credit application with id ${loanApplication.creditApplicationId} is not found`,
    )

    return null
  }

  return {
    available_credit: credit.creditDetails.availableCredit,
    held_amount: credit.creditDetails.creditHoldAmount,
    outstanding_amount: credit.creditDetails.drawOutstandingAmount,
    past_due_amount: credit.creditDetails.lateAmount,
    processing_amount: credit.creditDetails.processingAmount,
    maximum_credit_amount: credit.creditLimit,
    status: credit.status,
  }
}
