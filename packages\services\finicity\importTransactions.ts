import {
  AwsService,
  BankAccount,
  Company,
  connectToDatabase,
  FinicityTransactions,
  getEnvironmentVariables,
  initializeFinicity,
} from '@linqpal/common-backend'
import { SQSEvent, SQSRecord } from 'aws-lambda'
import { isObjectLike } from 'lodash'
import moment from 'moment-timezone'
import mongoose from 'mongoose'

const defaultEvent: SQSEvent = {
  Records: [{} as SQSRecord],
}

export default async function transactionsImport(
  event: SQSEvent = defaultEvent,
) {
  await getEnvironmentVariables()
  await connectToDatabase()
  console.log('Message Object', JSON.stringify(event))
  if (event?.Records) {
    return Promise.allSettled(event.Records.map(importTransactions))
  }
  return Promise.allSettled([prepareImport()])
}

async function prepareImport() {
  const companies = await Company.find({
    'finicity.customerId': mongoose.trusted({
      $exists: true,
    }),
    $or: [
      { 'finicity.lastImport': mongoose.trusted({ $in: [null, undefined] }) },
      {
        'finicity.lastImport': mongoose.trusted({
          $lt: moment().startOf('day').unix(),
        }),
      },
    ],
  })
  console.log('Filtered companies', companies.length)
  await Promise.allSettled(
    companies.map((c) =>
      AwsService.sendSQSMessage(
        'finicity-transactions-aggregation',
        JSON.stringify({ company_id: c.id }),
      ),
    ),
  )
}

async function importTransactions(record: SQSRecord) {
  const info: { company_id?: string } = isObjectLike(record.body)
    ? record.body
    : JSON.parse(record.body || '{}')
  if (!info.company_id) return
  return Promise.allSettled([importCompanyTransactions(info.company_id)])
}

export async function importCompanyTransactions(company_id: string) {
  const company = await Company.findById(company_id)
  if (!company?.finicity?.customerId) return
  const finicityReq = await initializeFinicity(false)

  let makeNextCall = false
  let i = 1

  const aggregationStatus = await refreshTransactionCallAndCheckCompletion(
    company.finicity.customerId,
  )
  if (!aggregationStatus) {
    console.log('no aggregation available after 5 mins')
    console.log('exiting job')
    return
  }

  do {
    const { transactions, moreAvailable } =
      await finicityReq.customers.getAllCustomerTransactions(
        company.finicity.customerId,
        moment().subtract(2, 'years'),
        moment(),
        i,
      )
    const ops = transactions.map((t) => ({
      updateOne: {
        filter: {
          transactionId: t.id.toString(),
          customerId: t.customerId,
          accountId: t.accountId,
        },
        update: {
          ...t,
          company_id: company.id,
          transactionId: t.id.toString(),
        },
        upsert: true,
      },
    }))
    const result = await FinicityTransactions.bulkWrite(ops)
    console.log(
      `Saved ${result.upsertedCount} transactions for ${
        company.name || company.legalName || company._id
      }`,
    )
    try {
      const companyRecord = await Company.findById(company.id)
      if (companyRecord) {
        if (!companyRecord.finicity) {
          companyRecord.finicity = { transactionsImportedCount: 0 }
        }
        if (result.upsertedCount > 0) {
          companyRecord.finicity.lastImport =
            transactions[transactions.length - 1]?.createdDate
          companyRecord.finicity.transactionImportCompleted = !moreAvailable
          companyRecord.finicity.transactionsImportedCount +=
            result.insertedCount
          companyRecord.markModified('finicity')
          await companyRecord.save()
        }
      }
    } catch (err) {
      console.log('Error in saving to db', err)
      return
    }
    makeNextCall = moreAvailable
    i += 1
  } while (makeNextCall)
}

async function refreshTransactionCallAndCheckCompletion(customerId: string) {
  let tryCount = 1
  let ifAccountsAggregated = false

  let keepCalling = true
  const timeoutObject = setTimeout(() => (keepCalling = false), 5 * 60 * 1000) // set to 5 mins

  do {
    console.log('Try no - ', tryCount++)
    const fin = await initializeFinicity()
    const accounts = await fin.accounts.getAccounts(customerId)
    if (accounts.length === 0) break
    for (const info of accounts) {
      const a = await BankAccount.findOne({ 'finicity.accountId': info.id })
      if (a?.finicity && a.finicity.syncState !== info.aggregationStatusCode) {
        a.finicity.syncState = info.aggregationStatusCode
        await a.save()
      }
    }
    // Function return once all the account has been aggregated
    ifAccountsAggregated = accounts.some(
      (account) =>
        account.aggregationSuccessDate || account.aggregationStatusCode > 0,
    )
    if (!ifAccountsAggregated) await timeout(5 * 1000) // next call after 5 sec
  } while (!ifAccountsAggregated && keepCalling)

  if (timeoutObject) {
    clearTimeout(timeoutObject)
  }
  return ifAccountsAggregated
}

async function timeout(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}
