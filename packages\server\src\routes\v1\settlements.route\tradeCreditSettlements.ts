import {
  Company,
  getLoanPricingPackage,
  Invoice,
} from '@linqpal/common-backend'
import { exceptions, InvoicePaymentType } from '@linqpal/models'
import { PipelineStage } from 'mongoose'
import { ControllerItem } from 'src/routes/controllerItem'
import {
  invoiceStatus,
  LOAN_APPLICATION_STATUS,
  OPERATION_STATUS,
  OPERATION_TYPES,
  PAYMENT_METHODS,
  SETTLEMENT_STATUS,
  TRANSACTION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { createPagination } from '@linqpal/common-backend'
import moment from 'moment-timezone'

const format = '%m/%d/%Y'
const timezone = 'America/Chicago'

export default {
  get: async (req, res) => {
    const company = await Company.findById(req.company!.id)

    const pricingPackage = await getLoanPricingPackage(
      company?.settings?.loanPricingPackageId,
    )

    if (!pricingPackage) {
      throw new exceptions.LogicalError('BNPL opted out.')
    }

    const pipeline: PipelineStage[] = [
      {
        $match: {
          company_id: req.company!.id,
          $or: [
            { paymentDetails: { $in: [null, undefined] } },
            {
              $and: [
                { paymentDetails: { $ne: null } },
                {
                  $or: [
                    { 'paymentDetails.paymentType': { $in: [null, undefined] } },
                    {
                      'paymentDetails.paymentType': {
                        $ne: InvoicePaymentType.FACTORING,
                      },
                    },
                  ],
                },
              ],
            },
          ],
        },
      },
      {
        $lookup: {
          from: 'operations',
          as: 'paymentOperation',
          let: { id: { $toString: '$_id' } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$owner_id', '$$id'] },
                    {
                      $in: [
                        '$status',
                        [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                      ],
                    },
                  ],
                },
                type: OPERATION_TYPES.INVOICE.PAYMENT,
                'metadata.payment_method': PAYMENT_METHODS.LOAN,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$paymentOperation',
          preserveNullAndEmptyArrays: false,
        },
      },
      {
        $lookup: {
          from: 'loanapplications',
          as: 'loanApplication',
          let: { invoiceId: { $toString: '$_id' } },
          pipeline: [
            {
              $addFields: {
                ids: {
                  $cond: {
                    if: {
                      $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
                    },
                    then: '$invoiceDetails.invoiceId',
                    else: ['$invoiceDetails.invoiceId'],
                  },
                },
              },
            },
            {
              $match: {
                $expr: { $in: ['$$invoiceId', '$ids'] },
                status: {
                  $in: [
                    LOAN_APPLICATION_STATUS.APPROVED,
                    LOAN_APPLICATION_STATUS.CLOSED,
                  ],
                },
                lms_id: { $exists: true },
              },
            },
            {
              $project: {
                lms_id: 1,
                status: 1,
                invoiceDetails: 1,
                metadata: 1,
                issueDate: 1,
                updatedAt: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$loanApplication',
          preserveNullAndEmptyArrays: false,
        },
      },
      // Find payment plan duration. Since November 2022 paymentPlan is saved right into loanApplication
      // This query is only to support legacy records. In November 2024 this part can be removed.
      {
        $lookup: {
          from: 'loanpaymentplans',
          as: 'paymentPlan',
          let: {
            paymentPlanId: {
              $convert: {
                input: '$loanApplication.invoiceDetails.paymentPlan',
                to: 'objectId',
                onError: null,
              },
            },
            paymentPlanName: '$loanApplication.invoiceDetails.paymentPlan',
          },
          pipeline: [
            {
              $match: {
                $expr: {
                  $or: [
                    { $eq: ['$_id', '$$paymentPlanId'] },
                    { $eq: ['$name', '$$paymentPlanName'] },
                  ],
                },
              },
            },
            { $project: { days: 1 } },
          ],
        },
      },
      {
        $unwind: {
          path: '$paymentPlan',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'customeraccounts',
          as: 'customer',
          let: {
            customer_account_id: {
              $convert: {
                input: '$customer_account_id',
                to: 'objectId',
                onError: null,
              },
            },
          },
          pipeline: [
            {
              $match: { $expr: { $eq: ['$_id', '$$customer_account_id'] } },
            },
            {
              $lookup: {
                from: 'companies',
                as: 'company',
                let: { company_id: '$company_id' },
                pipeline: [
                  {
                    $match: {
                      $expr: { $eq: ['$_id', { $toObjectId: '$$company_id' }] },
                    },
                  },
                ],
              },
            },
            {
              $unwind: {
                path: '$company',
                preserveNullAndEmptyArrays: true,
              },
            },
            {
              $project: {
                name: { $ifNull: ['$name', '$company.name'] },
                first_name: 1,
                last_name: 1,
                display_name: 1,
                phone: 1,
              },
            },
          ],
        },
      },
      {
        $unwind: {
          path: '$customer',
          preserveNullAndEmptyArrays: true,
        },
      },
      {
        $lookup: {
          from: 'operations',
          as: 'operations',
          let: { owner: { $toString: '$_id' } },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$owner_id', '$$owner'] },
                    {
                      $in: [
                        '$status',
                        [OPERATION_STATUS.SUCCESS, OPERATION_STATUS.PROCESSING],
                      ],
                    },
                  ],
                },
                type: {
                  $in: [
                    OPERATION_TYPES.INVOICE.PAYMENT,
                    OPERATION_TYPES.INVOICE.FINAL_PAYMENT,
                  ],
                },
              },
            },
          ],
        },
      },
      {
        $addFields: {
          operationsIds: {
            $map: {
              input: '$operations',
              as: 'op',
              in: { $toString: '$$op._id' },
            },
          },
        },
      },
      {
        $lookup: {
          from: 'transactions',
          as: 'transactions',
          let: { ops: '$operationsIds' },
          pipeline: [
            {
              $match: { $expr: { $in: ['$operation_id', '$$ops'] } },
            },
            {
              $project: {
                transactionNumber: '$metadata.transactionNumber',
                operation_id: 1,
                type: 1,
                payer_id: 1,
                payee_id: 1,
                amount: 1,
                fee: 1,
                date: 1,
                payment_method: 1,
                status: 1,
                createdAt: 1,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          advancePayment: { $arrayElemAt: ['$transactions', 0] },
        },
      },
      {
        $addFields: {
          finalPayment: {
            $cond: [
              { $eq: [{ $size: '$transactions' }, 2] },
              { $arrayElemAt: ['$transactions', 1] },
              undefined,
            ],
          },
        },
      },
      {
        $project: {
          id: { $toString: '$_id' },
          customer_account_id: 1,
          customer_user_id: 1,
          invoice_number: 1,
          invoice_date: {
            $dateToString: { date: '$invoice_date', format, timezone },
          },
          invoice_due_date: {
            $dateToString: { date: '$invoice_due_date', format, timezone },
          },
          loanApplication: 1,
          total_amount: 1,
          seen: 1,
          tax_amount: 1,
          material_subtotal: 1,
          expiration_date: 1,
          address: 1,
          addressType: 1,
          invoice_document: 1,
          document_name: 1,
          dismiss_reasons: 1,
          material_description: 1,
          note: 1,
          createdAt: 1,
          customer: 1,
          advancePayment: 1,
          recentPayment: 1,
          finalPayment: 1,
          // since 08.11.2022 loanPackage is saved into loanApplication,
          // so in december 2024 fallback with pricingPackage can be removed
          merchantFeePercentage: {
            $ifNull: [
              '$loanApplication.metadata.loanPackage.merchant',
              pricingPackage.metadata.merchant,
            ],
          },
          skipFinalPayment: {
            $cond: {
              if: {
                $eq: [
                  {
                    $ifNull: [
                      '$loanApplication.metadata.loanPackage.finalPayment',
                      pricingPackage.metadata.finalPayment,
                    ],
                  },
                  0,
                ],
              },
              then: true,
              else: false,
            },
          },
          term: {
            $ifNull: [
              '$loanApplication.metadata.paymentPlan.days',
              '$paymentPlan.days',
              {
                $cond: {
                  if: {
                    $isNumber: '$loanApplication.invoiceDetails.paymentPlan',
                  },
                  then: '$loanApplication.invoiceDetails.paymentPlan',
                  else: null,
                },
              },
              0,
            ],
          },
          transactionDate: {
            $ifNull: [
              '$loanApplication.issueDate',
              '$loanApplication.updatedAt',
            ],
          },
          advanceReleaseDate: {
            $ifNull: ['$advancePayment.date', '$advancePayment.createdAt'],
          },
          advanceSettledAmount: '$advancePayment.amount',
          advanceStatus: {
            $cond: {
              if: {
                $eq: ['$advancePayment.status', TRANSACTION_STATUS.SUCCESS],
              },
              then: SETTLEMENT_STATUS.SETTLED,
              else: SETTLEMENT_STATUS.IN_PROGRESS,
            },
          },
          finalStatus: {
            $cond: {
              if: {
                $eq: ['$finalPayment.status', TRANSACTION_STATUS.SUCCESS],
              },
              then: SETTLEMENT_STATUS.SETTLED,
              else: {
                $cond: {
                  if: { $gt: ['$finalPayment', null] },
                  then: SETTLEMENT_STATUS.IN_PROGRESS,
                  else: SETTLEMENT_STATUS.SCHEDULED,
                },
              },
            },
          },
        },
      },
      { $addFields: { status: projectInvoiceStatus() } },
      {
        $addFields: {
          merchantFee: {
            $round: [
              {
                $multiply: [
                  { $divide: ['$total_amount', 100] },
                  '$merchantFeePercentage',
                ],
              },
              2,
            ],
          },
        },
      },
      {
        $addFields: {
          settledAmount: {
            $subtract: ['$total_amount', '$merchantFee'],
          },
        },
      },
      {
        $addFields: {
          finalSettledAmount: {
            $cond: {
              if: { $eq: ['$skipFinalPayment', true] },
              then: 0,
              else: {
                $ifNull: [
                  '$finalPayment.amount',
                  {
                    $round: [
                      {
                        $subtract: ['$settledAmount', '$advanceSettledAmount'],
                      },
                      2,
                    ],
                  },
                ],
              },
            },
          },
        },
      },
      {
        $addFields: {
          finalReleaseDate: {
            $cond: {
              if: { $eq: ['$skipFinalPayment', false] },
              then: {
                $ifNull: [
                  '$finalPayment.date',
                  {
                    $dateAdd: {
                      startDate: '$transactionDate',
                      unit: 'day',
                      amount: '$term',
                    },
                  },
                ],
              },
              else: null,
            },
          },
        },
      },
      {
        $group: {
          _id: '$advancePayment.transactionNumber',
          transactionNumber: { $first: '$advancePayment.transactionNumber' },
          customer: { $first: '$customer' },
          invoices: { $addToSet: '$$ROOT' },
          invoiceAmount: { $sum: '$total_amount' },
          merchantFeePercentage: { $first: '$merchantFeePercentage' },
          merchantFee: { $sum: '$merchantFee' },
          term: { $first: '$term' },
          transactionDate: { $first: '$transactionDate' },
          settledAmount: { $sum: '$settledAmount' },
          advanceSettledAmount: { $sum: '$advanceSettledAmount' },
          advanceReleaseDate: { $first: '$advanceReleaseDate' },
          advanceStatus: { $first: '$advanceStatus' },
          skipFinalPayment: { $first: '$skipFinalPayment' },
          finalSettledAmount: { $sum: '$finalSettledAmount' },
          finalReleaseDate: { $first: '$finalReleaseDate' },
          finalStatus: { $first: '$finalStatus' },
        },
      },
      {
        $set: { invoiceAmount: { $round: ['$invoiceAmount', 2] } },
      },
    ]

    const dateFrom = req.query.dateFrom
      ? moment(req.query.dateFrom.toString(), 'MM/DD/YYYY').toDate()
      : moment().startOf('day').subtract(2, 'years').toDate()

    const dateTo = req.query.dateTo
      ? moment(req.query.dateTo.toString(), 'MM/DD/YYYY').endOf('day').toDate()
      : moment().startOf('day').add(10, 'years').toDate()

    applyFilterParams(req.query, pipeline, dateFrom, dateTo)

    pipeline.push({ $sort: { transactionDate: -1 } })

    const { paginationPipeline, pageSize } = createPagination(req.query)

    const result = await Invoice.aggregate([
      ...pipeline,
      {
        $facet: {
          overview: [
            {
              $group: {
                _id: null,
                paymentsCount: {
                  $sum: {
                    $cond: {
                      if: {
                        $and: [
                          shouldIncludeInOverview(
                            '$advanceStatus',
                            '$advanceReleaseDate',
                            dateFrom,
                            dateTo,
                          ),
                          shouldIncludeInOverview(
                            '$finalStatus',
                            '$finalReleaseDate',
                            dateFrom,
                            dateTo,
                          ),
                        ],
                      },
                      then: 2,
                      else: {
                        $cond: {
                          if: {
                            $or: [
                              shouldIncludeInOverview(
                                '$advanceStatus',
                                '$advanceReleaseDate',
                                dateFrom,
                                dateTo,
                              ),
                              shouldIncludeInOverview(
                                '$finalStatus',
                                '$finalReleaseDate',
                                dateFrom,
                                dateTo,
                              ),
                            ],
                          },
                          then: 1,
                          else: 0,
                        },
                      },
                    },
                  },
                },
                advanceSettledAmount: {
                  $sum: {
                    $cond: {
                      if: shouldIncludeInOverview(
                        '$advanceStatus',
                        '$advanceReleaseDate',
                        dateFrom,
                        dateTo,
                      ),
                      then: '$advanceSettledAmount',
                      else: 0,
                    },
                  },
                },
                finalSettledAmount: {
                  $sum: {
                    $cond: {
                      if: shouldIncludeInOverview(
                        '$finalStatus',
                        '$finalReleaseDate',
                        dateFrom,
                        dateTo,
                      ),
                      then: '$finalSettledAmount',
                      else: 0,
                    },
                  },
                },
                outstandingFinalAmount: {
                  $sum: {
                    $cond: {
                      if: {
                        $eq: ['$finalStatus', SETTLEMENT_STATUS.SCHEDULED],
                      },
                      then: '$finalSettledAmount',
                      else: 0,
                    },
                  },
                },
              },
            },
          ],
          total: [{ $count: 'count' }],
          items: paginationPipeline,
        },
      },
      { $unwind: { path: '$total', preserveNullAndEmptyArrays: true } },
      { $unwind: { path: '$overview', preserveNullAndEmptyArrays: true } },
    ])

    const { total, items, overview } = result[0]
    const count = total?.count ?? 0

    res.send({
      overview,
      items,
      count: pageSize > 0 ? count : items.length,
    })
  },
} as ControllerItem

function projectInvoiceStatus() {
  return {
    $cond: {
      if: {
        $and: [
          { $eq: ['$advanceStatus', SETTLEMENT_STATUS.SETTLED] },
          {
            $or: [
              { $eq: ['$skipFinalPayment', true] },
              { $eq: ['$finalStatus', SETTLEMENT_STATUS.SETTLED] },
            ],
          },
        ],
      },
      then: invoiceStatus.paid,
      else: invoiceStatus.paymentProcessing,
    },
  }
}

function applyFilterParams(
  query: any,
  pipeline: PipelineStage[],
  dateFrom: Date,
  dateTo: Date,
) {
  const { search, status } = query

  if (search?.toString()?.trim()) {
    pipeline.push(
      ...[
        {
          $set: {
            searchAmount: {
              $convert: {
                input: search,
                to: 'double',
                onError: -1,
              },
            },
          },
        },
        {
          $match: {
            $or: [
              { 'customer.name': { $regex: search, $options: 'i' } },
              { transactionNumber: { $regex: search, $options: 'i' } },
              {
                $expr: {
                  $or: [
                    { $eq: ['$invoiceAmount', '$searchAmount'] },
                    { $eq: ['$settledAmount', '$searchAmount'] },
                    { $eq: ['$advanceSettledAmount', '$searchAmount'] },
                    { $eq: ['$finalSettledAmount', '$searchAmount'] },
                  ],
                },
              },
              {
                invoices: {
                  $elemMatch: {
                    invoice_number: { $regex: search, $options: 'i' },
                  },
                },
              },
            ],
          },
        },
        { $unset: 'searchAmount' },
      ],
    )
  }

  if (status) {
    pipeline.push({
      $match: {
        $expr: {
          $or: [
            { $eq: ['$advanceStatus', status] },
            { $eq: ['$finalStatus', status] },
          ],
        },
      },
    })
  }

  pipeline.push(
    ...[
      {
        $match: {
          $or: [
            {
              advanceReleaseDate: {
                $gte: dateFrom,
                $lte: dateTo,
              },
            },
            {
              finalReleaseDate: {
                $gte: dateFrom,
                $lte: dateTo,
              },
            },
          ],
        },
      },
    ],
  )
}

function shouldIncludeInOverview(
  statusField: string,
  dateField: string,
  dateFrom: Date,
  dateTo: Date,
) {
  return {
    $and: [
      { $eq: [statusField, SETTLEMENT_STATUS.SETTLED] },
      { $gte: [dateField, dateFrom] },
      { $lte: [dateField, dateTo] },
    ],
  }
}
