import { Request } from 'express'
import {
  Company,
  Invoice,
  LMS,
  LoanApplication,
  createPagination,
} from '@linqpal/common-backend'
import {
  LOAN_STATUS,
  UI_LOAN_STATUS,
} from '@linqpal/models/src/dictionaries/loanStatuses'
import {
  ActiveDrawsSort,
  ActiveDrawsSortColumn,
  ActiveDrawsSortColumnType,
  DrawTypesFilter,
  DrawsStatusTypes,
  ITradeCreditActiveDraws,
  ITradeCreditInactiveDraws,
  InactiveDrawsSort,
  InactiveDrawsSortColumn,
  InactiveDrawsSortColumnType,
  getDrawType,
} from '@linqpal/models/src/dictionaries/tradeCredit'
import {
  DBSortingOrder,
  SortingOrder,
  DBSortingOrderType,
  PaginatedResult,
} from '@linqpal/models/src/dictionaries/global'
import {
  DECISION_STEPS,
  LOAN_APPLICATION_STATUS,
} from '@linqpal/models/src/dictionaries'
import { PipelineStage } from 'mongoose'

export const getInactiveDraws = async (
  req: Request,
): Promise<PaginatedResult<ITradeCreditInactiveDraws>> => {
  const companyId = req.company!.id
  const search = req.query?.search?.toString() ?? ''
  const statuses = req.query?.status
    ? [req.query.status]
    : [LOAN_STATUS.CLOSED, LOAN_STATUS.CANCELED]

  const type = req.query?.type || ''
  const { paginationPipeline, pageSize } = createPagination(req.query)
  const paginationArr =
    req.query?.pageSize && req.query?.page ? paginationPipeline : []

  const sortColumn = req.query?.sortColumn
    ? req.query.sortColumn.toString()
    : InactiveDrawsSort.ISSUE_DATE

  const sortDirection =
    req.query.sortDirection ||
    (sortColumn === InactiveDrawsSort.ISSUE_DATE
      ? SortingOrder.DESC
      : SortingOrder.ASC)

  const pipeline: PipelineStage[] = [
    {
      $match: {
        company_id: companyId, // '64a29cc89d5cb7037c35702d',
        lmsLoanStatus: {
          $in: statuses,
        },
        $or: [
          {
            status: {
              $in: [
                LOAN_APPLICATION_STATUS.APPROVED,
                LOAN_APPLICATION_STATUS.CLOSED,
                LOAN_APPLICATION_STATUS.EXPIRED,
              ],
            },
          },
          // Auto Trade Credit draws waiting for disbursement
          // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
          {
            $and: [
              { status: LOAN_APPLICATION_STATUS.PROCESSING },
              { lms_id: { $ne: null } },
              { 'metadata.repayment.autoTradeCreditEnabled': true },
              { 'progress.step': DECISION_STEPS.PENDING_MERCHANT_TRANSFER },
            ],
          },
        ],
        lms_id: { $exists: true },
        issueDate: { $ne: null },
        $expr: {
          $and: [
            { $ne: ['$lms_id', null] },
            { $ne: ['$lms_id', 0] },
            { $ne: ['$lms_id', '0'] },
            { $ne: ['$invoiceDetails.invoiceId', null] },
          ],
        },
      },
    },
    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'invoices',
        let: {
          invoiceIds: {
            $cond: {
              if: {
                $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
              },
              then: '$invoiceDetails.invoiceId',
              else: ['$invoiceDetails.invoiceId'],
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: { $in: [{ $toString: '$_id' }, '$$invoiceIds'] },
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              as: 'supplier',
              let: {
                supplierId: {
                  $convert: {
                    input: '$company_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } },
              ],
            },
          },
          {
            $unwind: {
              path: '$supplier',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              supplierName: {
                $ifNull: ['$supplierInvitationDetails.name', '$supplier.name'],
              },
            },
          },
          {
            $project: {
              invoice_number: 1,
              supplierName: 1,
              'supplierInvitationDetails.name': 1,
              company_id: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        invoice: { $arrayElemAt: ['$invoices', 0] },
      },
    },
    {
      $project: {
        lms_id: 1,
        invoices: 1,
        invoice: 1,
        _id: 1,
        issueDate: 1,
        lmsLoanStatus: 1,
        status: 1,
        metadata: 1,

        approvedAmount: { $ifNull: ['$approvedAmount', 0] },
      },
    },
  ]

  if (type) {
    switch (type) {
      case DrawTypesFilter.VIRTUAL_CARD:
        pipeline.push({
          $match: {
            $or: [
              { 'invoice.company_id': { $in: [null, undefined] } },
              { 'invoice.company_id': { $eq: '' } },
            ],
          },
        })
        break
      case DrawTypesFilter.CREDIT:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'metadata.repayment.autoTradeCreditEnabled': { $eq: true } },
            ],
          },
        })
        break
      case DrawTypesFilter.NO_SUPPLIER:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'invoice.supplierInvitationDetails.name': { $gt: '' } },
            ],
          },
        })
        break
      default:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'metadata.repayment.autoTradeCreditEnabled': { $ne: true } },
            ],
          },
        })
    }
  }

  if (search) {
    pipeline.push({
      $match: {
        $or: [
          {
            invoices: {
              $elemMatch: {
                invoice_number: { $regex: search, $options: 'i' },
              },
            },
          },
          {
            invoices: {
              $elemMatch: {
                supplierName: { $regex: search, $options: 'i' },
              },
            },
          },
        ],
      },
    })
  }

  pipeline.push({
    $sort: {
      [InactiveDrawsSortColumn[sortColumn as InactiveDrawsSortColumnType]]:
        DBSortingOrder[sortDirection as DBSortingOrderType],
    },
  })

  pipeline.push({
    $facet: {
      totalCount: [
        {
          $count: 'count',
        },
      ],
      paginatedResults: paginationArr,
    },
  })

  const [loanApps] = await LoanApplication.aggregate(pipeline)

  if (!loanApps?.paginatedResults?.length) {
    return {
      items: [],
      count: 0,
    }
  }

  const responseData: { items: ITradeCreditInactiveDraws[]; count: number } = {
    items: loanApps.paginatedResults.map((draw: any) => {
      return {
        id: draw._id,
        totalDrawAmount: draw.approvedAmount,
        status:
          draw.lmsLoanStatus === LOAN_STATUS.CLOSED
            ? DrawsStatusTypes.CLOSED
            : DrawsStatusTypes.CANCELLED,
        issueDate: draw.issueDate,
        supplier: draw.invoices[0].supplierName,
        type: getDrawType(draw),
        invoice: draw.invoices.map((invoice: any) => invoice.invoice_number),
        lmsId: draw.lms_id,
      }
    }),
    count:
      pageSize > 0
        ? loanApps.totalCount[0].count
        : loanApps.paginatedResults.length,
  }

  return responseData
}

export const getActiveDraws = async (
  req: Request,
): Promise<PaginatedResult<ITradeCreditActiveDraws>> => {
  const companyId = req.company!.id
  const search = req.query?.search?.toString() ?? ''
  /*const statuses = req.query?.status
    ? [req.query.type]
    : [LOAN_STATUS.CLOSED, LOAN_STATUS.CANCELED]*/
  const type = req.query?.type || ''
  const { paginationPipeline, pageSize } = createPagination(req.query)
  const paginationArr =
    req.query?.pageSize && req.query?.page ? paginationPipeline : []

  const sortColumn = req.query?.sortColumn
    ? req.query.sortColumn.toString()
    : ActiveDrawsSort.ISSUE_DATE

  const sortDirection =
    req.query.sortDirection ||
    (sortColumn === ActiveDrawsSort.ISSUE_DATE
      ? SortingOrder.DESC
      : SortingOrder.ASC)

  const pipeline: PipelineStage[] = [
    {
      $match: {
        company_id: companyId,
        lmsLoanStatus: {
          $nin: [LOAN_STATUS.CLOSED, LOAN_STATUS.CANCELED],
        },
        $or: [
          {
            status: {
              $in: [
                LOAN_APPLICATION_STATUS.APPROVED,
                LOAN_APPLICATION_STATUS.CLOSED,
                LOAN_APPLICATION_STATUS.EXPIRED,
              ],
            },
          },
          // Auto Trade Credit draws waiting for disbursement
          // TODO: VK: Introduce new loan app's PENDING_DISBURSEMENT status for ATC version #2
          {
            $and: [
              { status: LOAN_APPLICATION_STATUS.PROCESSING },
              { lms_id: { $ne: null } },
              { 'metadata.repayment.autoTradeCreditEnabled': true },
              { 'progress.step': DECISION_STEPS.PENDING_MERCHANT_TRANSFER },
            ],
          },
        ],
        lms_id: { $exists: true },
        issueDate: { $ne: null },
        $expr: {
          $and: [
            { $ne: ['$lms_id', null] },
            { $ne: ['$lms_id', 0] },
            { $ne: ['$lms_id', '0'] },
            { $ne: ['$invoiceDetails.invoiceId', null] },
          ],
        },
      },
    },
    {
      $lookup: {
        from: Invoice.collection.name,
        as: 'invoices',
        let: {
          invoiceIds: {
            $cond: {
              if: {
                $eq: [{ $type: '$invoiceDetails.invoiceId' }, 'array'],
              },
              then: '$invoiceDetails.invoiceId',
              else: ['$invoiceDetails.invoiceId'],
            },
          },
        },
        pipeline: [
          {
            $match: {
              $expr: { $in: [{ $toString: '$_id' }, '$$invoiceIds'] },
            },
          },
          {
            $lookup: {
              from: Company.collection.name,
              as: 'supplier',
              let: {
                supplierId: {
                  $convert: {
                    input: '$company_id',
                    to: 'objectId',
                    onError: null,
                  },
                },
              },
              pipeline: [
                { $match: { $expr: { $eq: ['$_id', '$$supplierId'] } } },
              ],
            },
          },
          {
            $unwind: {
              path: '$supplier',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $addFields: {
              supplierName: {
                $ifNull: ['$supplierInvitationDetails.name', '$supplier.name'],
              },
            },
          },
          {
            $project: {
              invoice_number: 1,
              supplierName: 1,
              'supplierInvitationDetails.name': 1,
              company_id: 1,
            },
          },
        ],
      },
    },
    {
      $addFields: {
        invoice: { $arrayElemAt: ['$invoices', 0] },
      },
    },
    {
      $project: {
        lms_id: 1,
        invoices: 1,
        invoice: 1,
        _id: 1,
        issueDate: 1,
        lmsLoanStatus: 1,
        status: 1,
        metadata: 1,

        approvedAmount: { $ifNull: ['$approvedAmount', 0] },
      },
    },
  ]

  if (type) {
    switch (type) {
      case DrawTypesFilter.VIRTUAL_CARD:
        pipeline.push({
          $match: {
            $or: [
              { 'invoice.company_id': { $in: [null, undefined] } },
              { 'invoice.company_id': { $eq: '' } },
            ],
          },
        })
        break
      case DrawTypesFilter.CREDIT:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'metadata.repayment.autoTradeCreditEnabled': { $eq: true } },
            ],
          },
        })
        break
      case DrawTypesFilter.NO_SUPPLIER:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'invoice.supplierInvitationDetails.name': { $gt: '' } },
            ],
          },
        })
        break
      default:
        pipeline.push({
          $match: {
            $and: [
              { 'invoice.company_id': { $gt: '' } },
              { 'metadata.repayment.autoTradeCreditEnabled': { $ne: true } },
            ],
          },
        })
    }
  }

  if (search) {
    pipeline.push({
      $match: {
        $or: [
          {
            invoices: {
              $elemMatch: {
                invoice_number: { $regex: search, $options: 'i' },
              },
            },
          },
          {
            invoices: {
              $elemMatch: {
                supplierName: { $regex: search, $options: 'i' },
              },
            },
          },
        ],
      },
    })
  }

  pipeline.push({
    $sort: {
      [ActiveDrawsSortColumn[sortColumn as ActiveDrawsSortColumnType]]:
        DBSortingOrder[sortDirection as DBSortingOrderType],
    },
  })

  pipeline.push({
    $facet: {
      totalCount: [
        {
          $count: 'count',
        },
      ],
      paginatedResults: paginationArr,
    },
  })

  const [loanApps] = await LoanApplication.aggregate(pipeline)
  const loanIds = loanApps.paginatedResults.map((e: any) => ({
    id: e.lms_id,
  }))
  const loans = await LMS.getLoansByIds({ ids: loanIds })

  if (!loanApps?.paginatedResults?.length) {
    return {
      items: [],
      count: 0,
    }
  }

  const responseData: { items: ITradeCreditActiveDraws[]; count: number } = {
    items: loanApps.paginatedResults.map((draw: any) => {
      const lms = loans.find((loan) => loan.id === draw.lms_id)
      return {
        id: draw._id,
        totalDrawAmount: draw.approvedAmount,
        issueDate: draw.issueDate,
        supplier: draw.invoices[0].supplierName,
        nextPaymentAmount: lms?.loanDetails?.nextPaymentAmount,
        nextPaymentDate: lms?.nextPaymentDate,
        amountDue: lms?.loanDetails?.lateAmount,
        type: getDrawType(draw),
        invoice: draw.invoices.map((invoice: any) => invoice.invoice_number),
        status:
          lms?.currentStatus === UI_LOAN_STATUS.DUE_NEXT
            ? DrawsStatusTypes.DUE_NEXT
            : DrawsStatusTypes.PAST_DUE,
        lmsId: draw.lms_id,
      }
    }),
    count:
      pageSize > 0
        ? loanApps.totalCount[0].count
        : loanApps.paginatedResults.length,
  }

  return responseData
}
